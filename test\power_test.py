#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂上电下电测试
测试机械臂的上电和下电功能
"""

import sys
import os
import time

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

def robot_initialize_and_power_on(socket_fd):
    """机器人初始化和上电流程"""
    print("开始机器人初始化和上电流程...")
    servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}

    try:
        # 清除错误
        print("步骤 1: 清除错误...")
        nrc.clear_error(socket_fd)
        time.sleep(0.5)

        # 获取当前伺服状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            current_state = result[1]
        else:
            current_state = servo_status

        print(f"步骤 2: 当前伺服状态为 {current_state} ({servo_names.get(current_state, '未知状态')})")

        # 根据状态执行相应操作
        if current_state == 0:  # 停止状态
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.5)
        elif current_state == 3:  # 运行状态
            nrc.set_servo_poweroff(socket_fd)
            time.sleep(1)

        # 执行上电
        print("步骤 3: 执行上电操作...")
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"上电失败！返回码: {result}")
            print("请检查安全回路、示教器模式、急停按钮等")
            return False

        time.sleep(1)
        print("✅ 机器人上电成功！")
        return True

    except Exception as e:
        print(f"初始化和上电流程失败: {e}")
        return False

def robot_power_off(socket_fd):
    """机器人下电流程"""
    print("开始机器人下电流程...")
    servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}

    try:
        # 获取当前伺服状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            current_state = result[1]
        else:
            current_state = servo_status

        print(f"当前伺服状态为 {current_state} ({servo_names.get(current_state, '未知状态')})")

        # 如果机器人在运行状态，执行下电
        if current_state == 3:  # 运行状态
            print("执行下电操作...")
            result = nrc.set_servo_poweroff(socket_fd)
            if result != 0:
                print(f"下电失败！返回码: {result}")
                return False

            time.sleep(1)
            print("✅ 机器人下电成功！")
            return True
        else:
            print(f"机器人当前不在运行状态，无需下电")
            return True

    except Exception as e:
        print(f"下电流程失败: {e}")
        return False

def power_cycle_test():
    """上电下电循环测试"""
    print("=" * 60)
    print("INEXBOT机械臂上电下电测试")
    print("=" * 60)
    
    socket_fd = -1
    
    try:
        # 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return False
        
        print(f"✅ 连接成功！Socket ID: {socket_fd}")
        
        # 测试上电
        print("\n" + "=" * 40)
        print("测试上电功能")
        print("=" * 40)
        power_on_success = robot_initialize_and_power_on(socket_fd)
        
        if power_on_success:
            # 等待一段时间
            print("\n⏳ 等待5秒...")
            time.sleep(5)
            
            # 测试下电
            print("\n" + "=" * 40)
            print("测试下电功能")
            print("=" * 40)
            power_off_success = robot_power_off(socket_fd)
            
            if power_off_success:
                print("\n✅ 上电下电测试完成！")
                return True
            else:
                print("\n❌ 下电测试失败！")
                return False
        else:
            print("\n❌ 上电测试失败！")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
        
    finally:
        # 断开连接
        if socket_fd > 0:
            print("\n🔌 正在断开连接...")
            try:
                result = nrc.disconnect_robot(socket_fd)
                print(f"断开连接结果: {result}")
                print("✅ 连接已断开")
            except Exception as e:
                print(f"断开连接时发生错误: {e}")

if __name__ == "__main__":
    print("🚀 开始INEXBOT机械臂上电下电测试...\n")
    
    success = power_cycle_test()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 上电下电测试完成！")
        print("✅ 机械臂上电功能正常")
        print("✅ 机械臂下电功能正常")
        print("✅ 状态检测功能正常")
    else:
        print("\n" + "=" * 60)
        print("❌ 上电下电测试失败")
        print("请检查:")
        print("1. 机械臂是否已开机并正常运行")
        print("2. 安全回路是否正常")
        print("3. 示教器是否在远程模式")
        print("4. 急停按钮是否释放")
        
    print("=" * 60)