#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 路径执行程序 (支持ABC旋转参数)

功能:
1. 读取一个标准的G-code文件。
2. 解析其中的G1移动指令，包括XYZ位置和ABC旋转角度。
3. 连接到INEXBOT机械臂。
4. 在指定的用户坐标系下，将G-code路径点转换为机械臂的movel指令并执行。
5. 正确处理ABC旋转参数，将A参数从度转换为弧度，并考虑负Z轴参考。
6. 监控每一步移动，确保完成后再执行下一步。
7. 任务结束后安全下电并断开连接。

注意: 此版本支持ABC旋转参数的完整处理，确保机械臂安全定位。
"""

import sys
import os
import time
import re
import math

# 添加lib目录到系统路径 (请根据您的项目结构调整)
# 假设此脚本与您之前的测试脚本在同一目录下
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# --- 全局参数配置 ---

# 1. 要执行的G-code文件
#    请将您想要执行的G-code文件放在此脚本的同级目录下
GCODE_FILE = "jiyi.Gcode"

# 2. 机械臂上对应的用户坐标系编号
#    这个编号必须与G-code路径所基于的坐标系严格对应
USER_COORD_NUMBER = 1

# 3. 运动参数
VELOCITY_PERCENT = 100  # 速度百分比 (0-100)
ACCEL_PERCENT = 40     # 加速度百分比 (0-100)
SMOOTHING_LEVEL = 0    # 平滑度 (0-8, 0表示精确定位)
TIMEOUT_SECONDS = 60   # 单步运动的超时时间 (秒)

# 4. 默认姿态参数 (工具垂直向下的典型姿态)
# 当G-code中没有ABC参数时使用的默认值
# 基于实际测量的用户坐标系安全垂直向下姿态: (180°, 0°, -90°)
DEFAULT_A_DEGREES = 180.0  # A轴默认角度 (度) - 垂直向下
DEFAULT_B_DEGREES = 0.0    # B轴默认角度 (度)
DEFAULT_C_DEGREES = -90.0  # C轴默认角度 (度) - 用户坐标系校正

# 机械臂安全垂直向下姿态 (弧度) - 用户坐标系
SAFE_VERTICAL_RX = math.pi        # 3.14弧度 = 180度，对应负Z方向
SAFE_VERTICAL_RY = 0.0            # 绕Y轴旋转角度 (弧度)
SAFE_VERTICAL_RZ = -math.pi/2     # -1.57弧度 = -90度，用户坐标系校正


def convert_gcode_angles_to_radians(a_deg, b_deg, c_deg):
    """
    将G-code中的ABC角度参数转换为机械臂所需的弧度制姿态角度。

    关键转换逻辑 (基于用户坐标系):
    - A参数: G-code中的A角度是相对于负Z轴的角度(度)
    - 机械臂的安全垂直向下姿态在用户坐标系中为(π, 0, -π/2)弧度
    - A=180°对应机械臂的RX=π(垂直向下)
    - A=0°对应机械臂的RX=0(水平)
    - C=-90°是用户坐标系中垂直向下的标准值

    实际测量的安全垂直向下姿态:
    - 用户坐标系: RX=180°, RY=0°, RZ=-90°
    - 笛卡尔坐标系: RX=180°, RY=0°, RZ=0°

    参数:
        a_deg (float): G-code中的A角度 (度)
        b_deg (float): G-code中的B角度 (度)
        c_deg (float): G-code中的C角度 (度)

    返回:
        tuple: (rx_rad, ry_rad, rz_rad) 机械臂姿态角度 (弧度，用户坐标系)
    """
    # A角度转换: G-code A角度直接转换为机械臂RX角度
    # A=180°时对应RX=π(垂直向下)，A=0°时对应RX=0(水平)
    rx_rad = math.radians(a_deg)

    # B和C角度直接转换为弧度
    # 注意: C=-90°是用户坐标系中垂直向下的标准值
    ry_rad = math.radians(b_deg)
    rz_rad = math.radians(c_deg)

    return rx_rad, ry_rad, rz_rad


def parse_gcode_file(filepath):
    """
    解析G-code文件，提取G1指令的路径点。
    返回一个包含XYZ位置和ABC旋转角度的字典列表。
    """
    print(f"📄 正在解析G-code文件: {filepath}")
    path = []

    # 正则表达式用于匹配G0/G1指令中的坐标轴和值（包括XYZABC）
    coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip().upper()
                # 忽略注释和空行
                if not line or line.startswith(';'):
                    continue

                if line.startswith('G1') or line.startswith('G0'):
                    coords = dict(coord_regex.findall(line))

                    # 检查是否包含必需的XYZ坐标
                    if 'X' in coords and 'Y' in coords and 'Z' in coords:
                        # 提取XYZ坐标
                        x = float(coords.get('X', 0.0))
                        y = float(coords.get('Y', 0.0))
                        z = float(coords.get('Z', 0.0))

                        # 提取ABC角度，如果不存在则使用默认值
                        a_deg = float(coords.get('A', DEFAULT_A_DEGREES))
                        b_deg = float(coords.get('B', DEFAULT_B_DEGREES))
                        c_deg = float(coords.get('C', DEFAULT_C_DEGREES))

                        # 转换ABC角度为机械臂所需的弧度制姿态
                        rx_rad, ry_rad, rz_rad = convert_gcode_angles_to_radians(a_deg, b_deg, c_deg)

                        point = {
                            'x': x,
                            'y': y,
                            'z': z,
                            'a_deg': a_deg,  # 保留原始G-code角度用于调试
                            'b_deg': b_deg,
                            'c_deg': c_deg,
                            'rx_rad': rx_rad,  # 转换后的机械臂姿态角度
                            'ry_rad': ry_rad,
                            'rz_rad': rz_rad,
                            'line_num': line_num,  # 记录行号用于调试
                            'has_abc': 'A' in coords or 'B' in coords or 'C' in coords
                        }
                        path.append(point)

        print(f"✅ 解析完成，共找到 {len(path)} 个路径点。")

        # 统计ABC参数的使用情况
        points_with_abc = sum(1 for p in path if p['has_abc'])
        points_with_defaults = len(path) - points_with_abc
        print(f"   - {points_with_abc} 个点包含ABC参数")
        print(f"   - {points_with_defaults} 个点使用默认ABC值 (A={DEFAULT_A_DEGREES}°, B={DEFAULT_B_DEGREES}°, C={DEFAULT_C_DEGREES}°)")

        return path
    except FileNotFoundError:
        print(f"❌ 错误：G-code文件未找到: {filepath}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None


# --- 从您提供的测试脚本中复用的核心函数 ---
# (为了代码完整性，这里复制了这些函数。您可以保持它们在外部lib中)

def wait_for_motion_complete(socket_fd, timeout_seconds=30):
    """等待机器人运动完成"""
    print("  ⏳ 正在等待机器人运动完成...", end="", flush=True)
    start_time = time.time()
    last_print_time = 0

    while time.time() - start_time < timeout_seconds:
        try:
            running_status = 0
            result = nrc.get_robot_running_state(socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                running_status = result[1]
            
            if running_status == 0:
                print(" ✅")
                return True
            
            if time.time() - last_print_time > 2: # 每2秒打印一次状态
                 print(".", end="", flush=True)
                 last_print_time = time.time()

            time.sleep(0.1)
        except Exception as e:
            print(f"\n  (检查运动状态时发生错误: {e})")
            time.sleep(0.5)

    print(" ❌ 超时!")
    return False

def robot_power_on_if_needed(socket_fd):
    """如果需要则执行上电操作"""
    try:
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        
        if servo_status == 3:
            print("✅ 机器人伺服已上电。")
            return True
        
        print("ℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        if servo_status == 0:
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.2)
        
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败！返回码: {result}。请检查安全回路、急停按钮等。")
            return False
        
        time.sleep(1.5)
        
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1 and result[1] == 3:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {result}")
            return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}")
        return False

def robot_power_off(socket_fd):
    """机器人下电操作"""
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print("✅ 机器人已下电。")
        return True
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")
        return False

def get_current_pose(socket_fd):
    """获取机械臂当前位姿"""
    try:
        pos = nrc.VectorDouble()
        # 参数：socketFd, coord(坐标系类型), pos
        # coord: 0=关节，1=直角，2=工具，3=用户
        # 这里使用1=直角坐标系获取笛卡尔位置
        result = nrc.get_current_position(socket_fd, 1, pos)
        if result == 0 and len(pos) >= 6:
            return [pos[i] for i in range(6)]
        else:
            print(f"❌ 获取当前位姿失败，错误码: {result}")
            return None
    except Exception as e:
        print(f"❌ 获取当前位姿时发生错误: {e}")
        return None


def execute_gcode_on_robot():
    """主执行函数"""
    print("=" * 60)
    print("INEXBOT机械臂 G-code 路径执行程序")
    print("=" * 60)

    # 1. 解析G-code文件
    gcode_path = parse_gcode_file(GCODE_FILE)
    if not gcode_path:
        return

    socket_fd = -1
    try:
        # 2. 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        # 3. 检查并上电机器人
        if not robot_power_on_if_needed(socket_fd):
            return

        # 4. 设置运动所需的用户坐标系
        print(f"ℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return
        time.sleep(0.2)

        # 5. 准备ABC参数处理
        print("ℹ️ 准备ABC旋转参数处理...")
        print(f"   默认ABC值: A={DEFAULT_A_DEGREES}°, B={DEFAULT_B_DEGREES}°, C={DEFAULT_C_DEGREES}°")
        print(f"   对应机械臂安全姿态: RX={SAFE_VERTICAL_RX:.3f}, RY={SAFE_VERTICAL_RY:.3f}, RZ={SAFE_VERTICAL_RZ:.3f} (弧度)")
        print("   将根据G-code中的ABC参数动态计算每个点的姿态")

        # 6. 循环执行路径点
        print("\n" + "=" * 40)
        print(f"🚀 即将开始执行 {len(gcode_path)} 个路径点的移动...")
        print("=" * 40)

        for i, point in enumerate(gcode_path):
            print(f"\n--- 移动到点 {i+1}/{len(gcode_path)} ---")

            # 使用G-code中的XYZ位置和转换后的ABC姿态角度
            target_pos = [point['x'], point['y'], point['z'], point['rx_rad'], point['ry_rad'], point['rz_rad']]

            print(f"  目标位置 (用户坐标系 {USER_COORD_NUMBER}): X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")

            # 显示G-code原始ABC参数和转换后的姿态
            if point['has_abc']:
                print(f"  G-code ABC: A={point['a_deg']:.3f}°, B={point['b_deg']:.3f}°, C={point['c_deg']:.3f}°")
            else:
                print(f"  使用默认ABC: A={point['a_deg']:.3f}°, B={point['b_deg']:.3f}°, C={point['c_deg']:.3f}°")

            print(f"  机械臂姿态 (弧度): RX={target_pos[3]:.3f}, RY={target_pos[4]:.3f}, RZ={target_pos[5]:.3f}")
            print(f"  机械臂姿态 (度): RX={math.degrees(target_pos[3]):.1f}°, RY={math.degrees(target_pos[4]):.1f}°, RZ={math.degrees(target_pos[5]):.1f}°")

            # 安全性检查：验证姿态角度是否在合理范围内
            if abs(target_pos[3]) > 2 * math.pi or abs(target_pos[4]) > 2 * math.pi or abs(target_pos[5]) > 2 * math.pi:
                print(f"  ⚠️ 警告：姿态角度超出正常范围，请检查ABC参数")

            move_cmd = nrc.MoveCmd()
            move_cmd.targetPosType = 1  # 笛卡尔坐标
            move_cmd.targetPosValue = nrc.VectorDouble()
            for val in target_pos:
                move_cmd.targetPosValue.append(val)
            
            move_cmd.coord = 3  # 用户坐标系类型
            move_cmd.userNum = USER_COORD_NUMBER
            move_cmd.velocity = VELOCITY_PERCENT
            move_cmd.acc = ACCEL_PERCENT
            move_cmd.dec = ACCEL_PERCENT
            move_cmd.pl = SMOOTHING_LEVEL

            # 发送直线运动指令
            result = nrc.robot_movel(socket_fd, move_cmd)
            if result != 0:
                print(f"❌ 移动命令发送失败，错误码: {result}")
                break # 发生错误，终止循环

            # 等待运动完成
            if not wait_for_motion_complete(socket_fd, timeout_seconds=TIMEOUT_SECONDS):
                print("❌ 运动超时，程序终止。")
                break
        
        print("\n" + "=" * 40)
        print("🎉 所有路径点执行完毕！")
        print("=" * 40)

    except Exception as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        # 7. 确保安全下电并断开连接
        if socket_fd > 0:
            robot_power_off(socket_fd)
            print("🔌 正在断开连接...")
            nrc.disconnect_robot(socket_fd)
            print("✅ 连接已断开。")

if __name__ == "__main__":
    execute_gcode_on_robot()

"""
=== ABC旋转参数处理功能总结 ===

本程序已成功实现对G-code中ABC旋转参数的完整处理：

1. **G-code解析增强**：
   - 支持解析G-code行中的A、B、C旋转参数
   - 格式示例：G1 X324.3790 Y196.7261 Z4.8310 A8.437 B0.000 C0.600 F3000
   - 对于缺少ABC参数的行，自动使用默认值：A=180°, B=0°, C=0°

2. **角度转换机制**：
   - A参数：G-code中的A角度(度) → 机械臂RX角度(弧度)
   - B参数：G-code中的B角度(度) → 机械臂RY角度(弧度)
   - C参数：G-code中的C角度(度) → 机械臂RZ角度(弧度)
   - 转换公式：弧度 = 度 * π/180

3. **安全性保证**：
   - A=180°对应机械臂垂直向下安全姿态(RX=π弧度)
   - 添加姿态角度范围检查，防止异常值
   - 保留原始ABC值用于调试和验证

4. **兼容性**：
   - 完全向后兼容：对于只有XYZ的G-code文件，自动使用默认ABC值
   - 混合支持：同一文件中可以有带ABC参数和不带ABC参数的行

5. **调试信息**：
   - 显示每个点的原始ABC参数和转换后的机械臂姿态
   - 统计ABC参数使用情况
   - 提供度和弧度两种单位的显示

使用说明：
- 确保G-code文件中的A参数正确表示相对于负Z轴的角度
- A=180°表示工具垂直向下（安全姿态）
- A=0°表示工具水平
- B和C参数分别控制绕Y轴和Z轴的旋转
"""