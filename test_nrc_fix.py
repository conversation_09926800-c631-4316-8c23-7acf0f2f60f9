#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试NRC接口修复
验证get_current_position函数是否正确
"""

import sys
import os
import importlib.util

# 动态导入修改后的模块
spec = importlib.util.spec_from_file_location("run_gcode_success", "test/run_gcode success.py")
run_gcode_success = importlib.util.module_from_spec(spec)

def test_nrc_interface():
    """测试NRC接口"""
    print("=== NRC接口测试 ===")
    
    try:
        # 尝试导入nrc_interface
        import nrc_interface as nrc
        print("✅ nrc_interface 导入成功")
        
        # 检查关键函数是否存在
        functions_to_check = [
            'get_current_position',
            'robot_movel', 
            'MoveCmd',
            'VectorDouble',
            'connect_robot'
        ]
        
        for func_name in functions_to_check:
            if hasattr(nrc, func_name):
                print(f"✅ {func_name} 函数存在")
            else:
                print(f"❌ {func_name} 函数不存在")
        
        # 测试VectorDouble创建
        try:
            pos = nrc.VectorDouble()
            for i in range(7):
                pos.append(0.0)
            print("✅ VectorDouble 创建和操作成功")
        except Exception as e:
            print(f"❌ VectorDouble 操作失败: {e}")
        
        # 测试MoveCmd创建
        try:
            move_cmd = nrc.MoveCmd()
            move_cmd.targetPosType = 1
            move_cmd.targetPosValue = nrc.VectorDouble()
            print("✅ MoveCmd 创建和操作成功")
        except Exception as e:
            print(f"❌ MoveCmd 操作失败: {e}")
            
    except ImportError as e:
        print(f"❌ nrc_interface 导入失败: {e}")
        return False
    
    return True

def test_module_import():
    """测试模块导入"""
    print("\n=== 模块导入测试 ===")
    
    try:
        spec.loader.exec_module(run_gcode_success)
        print("✅ run_gcode_success 模块导入成功")
        
        # 检查关键函数是否存在
        functions_to_check = [
            'parse_gcode_file',
            'convert_gcode_angles_to_radians',
            'execute_gcode_on_robot'
        ]
        
        for func_name in functions_to_check:
            if hasattr(run_gcode_success, func_name):
                print(f"✅ {func_name} 函数存在")
            else:
                print(f"❌ {func_name} 函数不存在")
                
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_gcode_parsing():
    """测试G-code解析"""
    print("\n=== G-code解析测试 ===")
    
    try:
        gcode_path = run_gcode_success.parse_gcode_file("jiyi.Gcode")
        if gcode_path:
            print(f"✅ G-code解析成功，共 {len(gcode_path)} 个点")
            
            # 检查第一个点的数据结构
            first_point = gcode_path[0]
            required_keys = ['x', 'y', 'z', 'rx_rad', 'ry_rad', 'rz_rad', 'has_abc']
            
            for key in required_keys:
                if key in first_point:
                    print(f"✅ 数据结构包含 {key}")
                else:
                    print(f"❌ 数据结构缺少 {key}")
                    
            return True
        else:
            print("❌ G-code解析失败")
            return False
            
    except Exception as e:
        print(f"❌ G-code解析测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("NRC接口修复验证测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    if test_nrc_interface():
        success_count += 1
    
    if test_module_import():
        success_count += 1
        
    if test_gcode_parsing():
        success_count += 1
    
    print(f"\n" + "=" * 60)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！NRC接口修复成功")
        print("\n现在可以安全运行主程序:")
        print('python "test\\run_gcode success.py"')
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    print("=" * 60)
