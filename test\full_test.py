#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂完整功能测试
包含所有已验证可用的功能
"""

import sys
import os
import time

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

def comprehensive_robot_test():
    """全面的机械臂功能测试"""
    print("=" * 60)
    print("INEXBOT机械臂完整功能测试")
    print("=" * 60)
    
    socket_fd = -1
    
    try:
        # 1. 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return False
        
        print(f"✅ 连接成功！Socket ID: {socket_fd}")
        
        # 2. 测试连接状态
        print("\n📡 测试连接状态...")
        status = nrc.get_connection_status(socket_fd)
        print(f"连接状态: {status} {'✅ 正常' if status == 0 else '❌ 异常'}")
        
        # 3. 获取库版本信息
        print("\n📚 获取库版本信息...")
        version = nrc.get_library_version()
        print(f"库版本: {version}")
        
        # 4. 获取伺服状态 ✅ 已验证可用
        print("\n⚙️  获取伺服状态...")
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
            status_desc = {
                0: "停止状态",
                1: "就绪状态", 
                2: "报警状态",
                3: "运行状态"
            }
            print(f"伺服状态: {servo_status} - {status_desc.get(servo_status, '未知状态')}")
        
        # 5. 获取当前模式 ✅ 已验证可用
        print("\n🎯 获取当前模式...")
        mode = 0
        result = nrc.get_current_mode(socket_fd, mode)
        
        if isinstance(result, list) and len(result) > 1:
            mode = result[1]
            mode_desc = {
                0: "示教模式",
                1: "远程模式",
                2: "运行模式"
            }
            print(f"当前模式: {mode} - {mode_desc.get(mode, '未知模式')}")
        
        # 6. 获取机器人类型 ⚠️ 暂时跳过
        print("\n🤖 获取机器人类型...")
        print("⚠️ 暂时跳过此功能 - API接口参数类型问题待解决")
        
        # 7. 获取当前位置 ✅ 已验证可用
        print("\n📍 获取当前位置...")
        pos = nrc.VectorDouble()
        for i in range(7):
            pos.append(0.0)
        
        result = nrc.get_current_position(socket_fd, 0, pos)  # 0=关节坐标
        if result == 0:
            print("当前关节位置 (度):")
            for i in range(6):  # 显示前6个关节
                print(f"  关节{i+1}: {pos[i]:8.3f}°")
        
        # 8. 获取当前坐标系
        print("\n🗺️  获取当前坐标系...")
        coord = 0
        result = nrc.get_current_coord(socket_fd, coord)
        
        if isinstance(result, list) and len(result) > 1:
            coord = result[1]
            coord_desc = {
                0: "关节坐标系",
                1: "直角坐标系",
                2: "工具坐标系",
                3: "用户坐标系"
            }
            print(f"当前坐标系: {coord} - {coord_desc.get(coord, '未知坐标系')}")
        
        # 9. 获取速度设置
        print("\n🚀 获取当前速度...")
        speed = 0
        result = nrc.get_speed(socket_fd, speed)
        
        if isinstance(result, list) and len(result) > 1:
            speed = result[1]
            print(f"当前速度: {speed}%")
        
        # 10. 获取工具手编号
        print("\n🔧 获取工具手编号...")
        tool_num = 0
        result = nrc.get_tool_hand_number(socket_fd, tool_num)
        
        if isinstance(result, list) and len(result) > 1:
            tool_num = result[1]
            print(f"当前工具手: {tool_num}")
        
        # 11. 获取用户坐标编号
        print("\n👤 获取用户坐标编号...")
        user_num = 0
        result = nrc.get_user_coord_number(socket_fd, user_num)
        
        if isinstance(result, list) and len(result) > 1:
            user_num = result[1]
            print(f"当前用户坐标: {user_num}")
        
        # 12. 测试连接稳定性
        print("\n🔄 测试连接稳定性...")
        for i in range(3):
            status = nrc.get_connection_status(socket_fd)
            print(f"  第{i+1}次状态检查: {'✅' if status == 0 else '❌'}")
            time.sleep(0.5)
        
        print("\n✅ 所有功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
        
    finally:
        # 断开连接
        if socket_fd > 0:
            print("\n🔌 正在断开连接...")
            try:
                result = nrc.disconnect_robot(socket_fd)
                print(f"断开连接结果: {result}")
                print("✅ 连接已断开")
            except Exception as e:
                print(f"断开连接时发生错误: {e}")

def test_connection_reliability():
    """测试连接可靠性"""
    print("\n" + "=" * 60)
    print("连接可靠性测试 - 快速连接/断开")
    print("=" * 60)
    
    success_count = 0
    total_tests = 5
    
    for i in range(total_tests):
        print(f"\n第 {i+1}/{total_tests} 次连接测试:")
        
        try:
            # 连接
            socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
            if socket_fd <= 0:
                print(f"  ❌ 连接失败")
                continue
                
            print(f"  ✅ 连接成功 (Socket: {socket_fd})")
            
            # 快速状态检查
            status = nrc.get_connection_status(socket_fd)
            if status == 0:
                print(f"  ✅ 状态正常")
                success_count += 1
            else:
                print(f"  ❌ 状态异常: {status}")
            
            # 断开连接
            nrc.disconnect_robot(socket_fd)
            print(f"  ✅ 断开完成")
            
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
        
        time.sleep(1)  # 等待1秒
    
    print(f"\n📊 可靠性测试结果: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")

if __name__ == "__main__":
    print("🚀 开始INEXBOT机械臂完整功能测试...\n")
    
    # 全面功能测试
    success = comprehensive_robot_test()
    
    if success:
        # 连接可靠性测试
        test_connection_reliability()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print("✅ 机械臂通讯连接正常")
        print("✅ 可以正常获取机械臂状态信息")
        print("✅ 可以获取位置、模式、速度等参数")
        print("⚠️ 机器人类型获取功能待完善")
        print("✅ TCP连接稳定可靠")
        print("✅ 适合进行后续开发工作")
    else:
        print("\n" + "=" * 60)
        print("❌ 功能测试失败")
        print("请检查:")
        print("1. 机械臂是否已开机并正常运行")
        print("2. 网络连接是否正常")
        print("3. IP地址配置是否正确")
        print("4. 防火墙设置是否允许连接")
        
    print("=" * 60)
