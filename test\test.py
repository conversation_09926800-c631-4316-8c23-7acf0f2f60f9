import sys
import os
import time

sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

import nrc_interface as nrc

socket_fd = nrc.connect_robot("192.168.1.13", "6001")
print("✅ 连接成功！Socket ID:", socket_fd)

nrc.clear_error(socket_fd)
print("✅ 系统错误已清除")

nrc.set_servo_state(socket_fd, 1)
time.sleep(0.5)  # 等待状态切换
print("✅ 伺服已就绪")

nrc.set_servo_poweron(socket_fd)
time.sleep(1)  # 等待上电完成
print("✅ 机器人上电成功")


target_x = 600
target_y = 0
target_z = 800
target_rx_deg = 3.14
target_ry_deg = 0
target_rz_deg = 0
move_cmd = nrc.MoveCmd()
move_cmd.targetPosType = 1      # 目标类型: 1=直角坐标
move_cmd.targetPosValue = nrc.VectorDouble()
target_pose = [target_x, target_y, target_z, target_rx_deg, target_ry_deg, target_rz_deg]
for val in target_pose:
    move_cmd.targetPosValue.append(val)
move_cmd.velocity = 20          # 速度
move_cmd.coord = 1              # 坐标系: 3=用户坐标系
move_cmd.pl=1
result_code = nrc.robot_movel(socket_fd, move_cmd)

time.sleep(4)  
nrc.set_servo_poweroff(socket_fd)
nrc.disconnect_robot(socket_fd)
print("✅ 连接已断开")
